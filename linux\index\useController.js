import { ref, reactive, watch } from 'vue';
import { getServerList, getServerDetail as getServerDetailApi } from '@/api/serverList';
import { useConfigStore } from '@/store/modules/config';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { getChartColorStops } from '@/pages/index/serverList/useController';
import { $t } from '@/locale/index.js';

// ==================== 网络数据状态 ====================
// 网络流量数据
export const upFlow = ref('0');
export const downFlow = ref('0');
export const upTotal = ref('0');
export const downTotal = ref('0');
export const networkData = reactive({
	xData: [], // 时间
	upData: [], // 上行数据
	downData: [], // 下行数据
});

// 磁盘IO数据
export const readBytes = ref('0');
export const writeBytes = ref('0');
export const ioCount = ref(0);
export const ioDelay = ref(0);
export const ioDelayColor = ref('#20a53a');
export const diskIOData = reactive({
	xData: [], // 时间
	readData: [], // 读取数据
	writeData: [], // 写入数据
});

// 图表数据 - 适配ECharts组件格式
export const networkChartData = reactive({
	categories: [], // x轴数据
	series: [
		{
			name: $t('linux.network.up'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
		{
			name: $t('linux.network.down'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
	],
	color: ['#ff8c00', '#1e90ff'],
});

// 磁盘IO图表数据 - 适配ECharts组件格式
export const diskIOChartData = reactive({
	categories: [], // x轴数据
	series: [
		{
			name: $t('linux.io.read'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
		{
			name: $t('linux.io.write'),
			type: 'line',
			smooth: true,
			showSymbol: false,
			symbolSize: 8,
			lineStyle: {
				width: 3,
			},
			data: [],
		},
	],
	color: ['#FF4683', '#6CC0CF'],
});

// 定时器ID
let timer = null;

// ==================== 网络相关方法 ====================
export const networkInfo = ref({});

// 版本比较函数
export const compareVersion = (version1, version2) => {
	if (!version1 || !version2) return false;

	const v1Parts = version1.split('.').map(Number);
	const v2Parts = version2.split('.').map(Number);

	// 补齐版本号位数
	const maxLength = Math.max(v1Parts.length, v2Parts.length);
	while (v1Parts.length < maxLength) v1Parts.push(0);
	while (v2Parts.length < maxLength) v2Parts.push(0);

	// 逐位比较
	for (let i = 0; i < maxLength; i++) {
		if (v1Parts[i] > v2Parts[i]) return 1;
		if (v1Parts[i] < v2Parts[i]) return -1;
	}
	return 0;
};

// 检查是否支持节点管理功能
export const isNodeManagementSupported = ref(false);

export const getNetwork = async () => {
	const { panelVersion } = useConfigStore().getReactiveState();
	try {
		const res = await getServerList();
		networkInfo.value = res;
		panelVersion.value = res.version;

		// 检查版本是否支持节点管理功能（需要 >= 9.7.0）
		isNodeManagementSupported.value = compareVersion(res.version, '9.7.0') >= 0;

		await getServerDetail(res);
		await getServerStatistics(res);
	} catch (error) {
		console.log(error);
	}
};

// 获取服务器统计
export const websiteTotal = ref(0);
export const safetyTotal = ref(0);
export const databaseTotal = ref(0);
export const getServerStatistics = async (info) => {
	try {
		websiteTotal.value = info.site_total;
		databaseTotal.value = info.database_total;
		safetyTotal.value = await getServerDetailApi();
	} catch (error) {}
};

// 服务器磁盘列表
export const diskList = ref([]);
// 获取服务器详情的值
export const chartMap = ref({});
export const getServerDetail = async (info) => {
	try {
		// 负载
		let loadCount =
			Math.round((info.load.one / info.load.max) * 100) > 100
				? 100
				: Math.round((info.load.one / info.load.max) * 100);
		loadCount = loadCount < 0 ? 0 : loadCount;
		chartMap.value['load'] = handleServerInfo(loadCount, 'load');
		// cpu
		let cpuCount = info.cpu[0];
		chartMap.value['cpu'] = handleServerInfo(cpuCount, 'cpu');
		// 内存
		const memCount = Math.round((info.mem.memRealUsed / info.mem.memTotal) * 1000) / 10; // 返回 memRealUsed 占 memTotal 的百分比
		chartMap.value['mem'] = handleServerInfo(memCount, 'mem');
		diskList.value = info.disk;
		let diskJson = [];
		for (let i = 0; i < diskList.value.length; i++) {
			let ratio = diskList.value[i].size[3];
			ratio = parseFloat(ratio.substring(0, ratio.lastIndexOf('%')));
			let diskInfo = handleDiskInfo(ratio, i);
			diskJson.push(diskInfo);
		}
		chartMap.value['disk'] = diskJson;
		updateChartData();
		handlePicker(info);
	} catch (error) {
		console.log(error);
	}
};

// 处理磁盘信息
export const handleDiskInfo = (ratio, index) => {
	try {
		let diskInfo = {};
		// 记录实际负载值
		diskInfo.val = ratio;

		diskInfo.path = networkInfo.value.disk[index].path;

		diskInfo.title = networkInfo.value.disk[index].size[0];

		return diskInfo;
	} catch (error) {}
};
// 处理对应的服务器信息
export const handleServerInfo = (number, name) => {
	try {
		// 根据负载区间判断状态
		const loadList = [
			{ val: 90, title: $t('linux.blocked'), color: '#dd2f00' },
			{ val: 80, title: $t('linux.slow'), color: '#ff9900' },
			{ val: 70, title: $t('linux.normal'), color: '#20a53a' },
			{ val: 30, title: $t('linux.smooth'), color: '#20a53a' },
		];

		let activeInfo = {};

		// 从高到低匹配第一个符合条件的负载区间
		for (let i = 0; i < loadList.length; i++) {
			if (number >= loadList[i].val) {
				activeInfo = { ...loadList[i] };
				break;
			} else if (number <= 30) {
				activeInfo = { ...loadList[3] };
				break;
			}
		}

		// 记录实际负载值
		activeInfo.val = number;

		switch (name) {
			case 'load':
				activeInfo.title = activeInfo.title;
				break;
			case 'cpu':
				activeInfo.title = `${networkInfo.value.cpu[1]} ${$t('linux.cores')}`;
				break;
			case 'mem':
				activeInfo.title = `${Math.round(networkInfo.value.mem.memTotal / 1024)} G`;
				break;
		}

		return activeInfo;
	} catch (error) {
		console.error('处理服务器信息出错:', error);
		return null;
	}
};

// 根据IO延迟设置颜色
export const updateIODelayColor = (delay) => {
	if (delay > 100 && delay < 1000) {
		ioDelayColor.value = '#ff9900';
	} else if (delay >= 1000) {
		ioDelayColor.value = '#dd2f00';
	} else {
		ioDelayColor.value = '#20a53a';
	}
};

// 格式化字节大小
export const formatBytes = (bytes) => {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取当前时间格式化字符串
export const getCurrentTime = () => {
	const now = new Date();
	const hours = now.getHours().toString().padStart(2, '0');
	const minutes = now.getMinutes().toString().padStart(2, '0');
	const seconds = now.getSeconds().toString().padStart(2, '0');
	return `${hours}:${minutes}:${seconds}`;
};

// 更新图表数据
export const updateChartData = () => {
	// 模拟获取网络和磁盘IO数据
	const currentTime = getCurrentTime();
	if (networkValue.value === 'ALL') {
		upFlow.value = networkInfo.value.up;
		downFlow.value = networkInfo.value.down;
	} else {
		upFlow.value = networkInfo.value.network[networkValue.value].up;
		downFlow.value = networkInfo.value.network[networkValue.value].down;
	}

	// 添加新数据点，并限制为5个数据点
	if (networkData.xData.length >= 5) {
		networkData.xData.shift();
		networkData.upData.shift();
		networkData.downData.shift();
	}
	networkData.xData.push(currentTime);
	networkData.upData.push(upFlow.value);
	networkData.downData.push(downFlow.value);

	// 更新网络图表数据 - 使用深拷贝触发视图更新
	networkChartData.categories = [...networkData.xData];
	networkChartData.series[0].data = [...networkData.upData];
	networkChartData.series[1].data = [...networkData.downData];

	// 磁盘IO数据
	let ioData = networkInfo.value.iostat[ioValue.value];
	readBytes.value = ioData.read_bytes;
	writeBytes.value = ioData.write_bytes;

	// ioCount.value = Math.floor(Math.random() * 100);
	// ioDelay.value = Math.floor(Math.random() * 200);
	// updateIODelayColor(ioDelay.value);

	// 添加新数据点，并限制为5个数据点
	if (diskIOData.xData.length >= 5) {
		diskIOData.xData.shift();
		diskIOData.readData.shift();
		diskIOData.writeData.shift();
	}
	diskIOData.xData.push(currentTime);
	diskIOData.readData.push(readBytes.value / 1024 / 1024).toFixed(2);
	diskIOData.writeData.push(writeBytes.value / 1024 / 1024).toFixed(2);

	// 更新磁盘IO图表数据 - 使用深拷贝触发视图更新
	diskIOChartData.categories = [...diskIOData.xData];
	diskIOChartData.series[0].data = [...diskIOData.readData];
	diskIOChartData.series[1].data = [...diskIOData.writeData];

	// 强制更新图表配置，确保x轴数据刷新
	chartOpts.xAxis.data = [...networkChartData.categories];
};

// 根据类型初始化图表数据格式
export const initChartByType = (type) => {
	if (type === 0) {
		// 网络图表
		networkChartData.series.forEach((item) => {
			item.type = 'line';
		});
	} else {
		// 磁盘IO图表
		diskIOChartData.series.forEach((item) => {
			item.type = 'line';
		});
	}
};

// 生成初始数据
export const initData = () => {
	// 重置数据数组，确保每次调用都是全新的数据
	networkData.xData = [];
	networkData.upData = [];
	networkData.downData = [];
	diskIOData.xData = [];
	diskIOData.readData = [];
	diskIOData.writeData = [];
	networkList.value = [];
	ioList.value = [];
	networkValue.value = 'ALL';
	ioValue.value = 'ALL';
	networkName.value = $t('linux.all');
	ioName.value = $t('linux.all');
	subsecType.value = 0;
};

// 启动定时器
export const startTimer = () => {
	// 确保先停止任何可能存在的定时器
	stopTimer();

	// 设置定时器，每3秒更新一次数据
	timer = setInterval(getNetwork, 3000);
};

// 停止定时器
export const stopTimer = () => {
	// 清除定时器
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
};

// ==================== 图表选项 ====================
// 图表基础配置
export const chartOpts = reactive({
	grid: {
		bottom: 30,
		right: 20,
	},
	xAxis: {
		axisLine: {},
		axisLabel: {
			fontSize: 10,
			margin: 10,
			rotate: 0,
		},
		type: 'category',
		data: [],
	},
	yAxis: {
		scale: true, // 启用y轴自动缩放
		nameTextStyle: {
			padding: [0, 0, 0, 0],
			color: '#999999',
			fontSize: 10,
		},
		axisLabel: {
			color: '#999999',
			fontSize: 10,
			margin: 10,
		},
		splitLine: {
			lineStyle: {
				color: '#999999',
			},
		},
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: {
			type: 'cross',
		},
	},
	legend: {
		top: 5,
		left: 'center',
		textStyle: {
			color: '#999999',
			fontSize: 10,
		},
		itemWidth: 15,
		itemHeight: 10,
		itemGap: 20,
	},
	title: {
		left: 'center',
		top: 10,
		textStyle: {
			fontSize: 10,
		},
	},
});

// 网络图表专属配置
export const getNetworkChartOpts = () => {
	return {
		...chartOpts,
		xAxis: {
			...chartOpts.xAxis,
			data: networkChartData.categories,
		},
		yAxis: {
			...chartOpts.yAxis,
			name: $t('linux.unitKBS'),
			axisLabel: {
				...chartOpts.yAxis.axisLabel,
				formatter: (value) => `${value} KB/s`,
			},
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
	};
};

// IO图表专属配置
export const getDiskIOChartOpts = () => {
	return {
		...chartOpts,
		xAxis: {
			...chartOpts.xAxis,
			data: diskIOChartData.categories,
		},
		yAxis: {
			...chartOpts.yAxis,
			name: $t('linux.unitMBS'),
			axisLabel: {
				...chartOpts.yAxis.axisLabel,
				formatter: (value) => `${value} MB/s`,
			},
		},
		title: {
			...chartOpts.title,
			text: ' ',
		},
	};
};

// ==================== 区域控制 ====================
export const picker = ref(null);
export const subsecType = ref(0); // 0: 网络, 1: 磁盘IO
export const networkList = ref([]);
export const networkName = ref($t('linux.all'));
export const networkValue = ref('ALL');
export const ioList = ref([]);
export const ioName = ref($t('linux.all'));
export const ioValue = ref('ALL');
export const defaultIndex = ref([0]);

export const openPicker = () => {
	let index = -1;

	if (subsecType.value === 0) {
		index = networkList.value.findIndex((item) => item.value === networkValue.value);
	} else {
		index = ioList.value.findIndex((item) => item.value === ioValue.value);
	}

	// 确保找到有效索引，否则默认为第一项
	defaultIndex.value = [index >= 0 ? index : 0];

	// 确保picker存在再调用open方法
	picker.value?.open();
};

const handlePicker = (res) => {
	// 网卡
	if (networkList.value.length == 0 && ioList.value.length == 0) {
		for (let key in res.iostat) {
			let ioItem = {
				title: key == 'ALL' ? $t('linux.all') : key,
				value: key,
			};
			ioList.value.push(ioItem);
		}

		networkList.value.push({
			title: $t('linux.all'),
			value: 'ALL',
		});

		for (let key in res.network) {
			let netItem = {
				title: key,
				value: key,
			};
			networkList.value.push(netItem);
		}
	}
};

export const confirm = (e) => {
	if (subsecType.value === 0) {
		networkValue.value = e.value[0].value;
		networkName.value = e.value[0].title;
	} else {
		ioValue.value = e.value[0].value;
		ioName.value = e.value[0].title;
	}
};

// 当subsection切换时
export const onSectionChange = (index) => {
	subsecType.value = index;
	if (index === 0) {
		networkValue.value = 'ALL';
		networkName.value = $t('linux.all');
	} else {
		ioValue.value = 'ALL';
		ioName.value = $t('linux.all');
	}
};

export const handleModuleAction = (type) => {
	throttle(() => {
		switch (type) {
			case 'website':
				uni.navigateTo({
					url: '/linux/website/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'database':
				uni.navigateTo({
					url: '/linux/database/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'file':
				uni.navigateTo({
					url: '/linux/files/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'security':
				uni.navigateTo({
					url: '/linux/firewall/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'terminal':
				uni.navigateTo({
					url: '/pages/ssh/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'control':
				uni.navigateTo({
					url: '/linux/control/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'ssh':
				uni.navigateTo({
					url: '/linux/ssh/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'setting':
				uni.navigateTo({
					url: '/linux/setting/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'monitorReport':
				uni.navigateTo({
					url: '/linux/monitorReport/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'nginx':
				uni.navigateTo({
					url: '/linux/nginx/index',
					animationType: 'zoom-fade-out',
				});
				break;
			case 'node':
				uni.navigateTo({
					url: '/linux/node/index',
					animationType: 'zoom-fade-out',
				});
				break;
		}
	});
};

// ==================== 监听数据变化 ====================
// 监听网络和磁盘IO数据变化
watch(
	() => ([...networkChartData.categories], [...diskIOChartData.categories]),
	() => {
		// 确保图表配置的xAxis数据也同步更新
		if (subsecType.value === 0) {
			// 网络图表当前激活
			chartOpts.xAxis.data = [...networkChartData.categories];
		} else {
			// 磁盘IO图表当前激活
			chartOpts.xAxis.data = [...diskIOChartData.categories];
		}
	},
	{ deep: true },
);

// 监听子系统类型变化，切换对应的x轴数据
watch(
	() => subsecType.value,
	(newType) => {
		if (newType === 0) {
			// 切换到网络图表
			chartOpts.xAxis.data = [...networkChartData.categories];
		} else {
			// 切换到磁盘IO图表
			chartOpts.xAxis.data = [...diskIOChartData.categories];
		}
	},
);

// 获取磁盘列表名称
export const getDiskListName = (path) => {
	if (path === '/') {
		return '/';
	}
	const parts = path.split('/').filter((part) => part.length > 0);
	if (parts.length > 0) {
		return '/' + parts[parts.length - 1];
	}
	// 如果路径无效或为空，也返回 '/'
	return '/';
};

// 获取基础图表配置
export const getBaseChartConfig = (usage, label) => {
	// 确保usage是数字
	usage = parseFloat(usage) || 0;

	// 创建颜色渐变配置
	const colorStops = getChartColorStops(usage);

	// 创建仪表盘数据
	const gaugeData = [
		{
			value: usage,
			name: label,
			title: {
				show: false,
			},
			detail: {
				valueAnimation: true,
				offsetCenter: [0, '0%'],
				fontSize: 10,
				color: '#A7A7A7',
				formatter: '{value}%',
			},
			itemStyle: {
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [...colorStops], // 使用深拷贝
				},
			},
		},
	];

	// 返回图表配置
	return {
		series: [
			{
				type: 'gauge',
				radius: '100%',
				startAngle: 90,
				endAngle: -270,
				pointer: {
					show: false,
				},
				progress: {
					show: true,
					overlap: false,
					roundCap: true,
					clip: false,
					itemStyle: {
						borderWidth: 10,
					},
				},
				axisLine: {
					lineStyle: {
						width: 6,
					},
				},
				axisTick: {
					show: false,
				},
				splitLine: {
					show: false,
				},
				axisLabel: {
					show: false,
					fontSize: 9,
				},
				title: {
					show: false,
				},
				data: gaugeData,
				detail: {
					valueAnimation: true,
					fontSize: 10,
					color: '#A7A7A7',
					formatter: '{value}%',
					borderRadius: 20,
				},
				animation: true,
				animationDuration: 1500,
				animationDurationUpdate: 1000,
				animationEasing: 'cubicInOut',
			},
		],
	};
};
